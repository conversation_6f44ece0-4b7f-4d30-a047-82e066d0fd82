<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/light_gray"
    tools:context=".MainActivity">

    <!-- Display -->
    <TextView
        android:id="@+id/display"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/white"
        android:gravity="center_vertical|end"
        android:padding="24dp"
        android:text="@string/display_hint"
        android:textColor="@color/black"
        android:textSize="32sp"
        android:layout_marginBottom="16dp"
        android:elevation="2dp" />

    <!-- Button Grid -->
    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:columnCount="4"
        android:rowCount="5">

        <!-- Row 1: Clear and operators -->
        <Button
            android:id="@+id/btn_clear"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnSpan="2"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/purple_500"
            android:text="@string/button_clear"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_divide"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/teal_700"
            android:text="@string/button_divide"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_multiply"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/teal_700"
            android:text="@string/button_multiply"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <!-- Row 2: 7, 8, 9, - -->
        <Button
            android:id="@+id/btn_7"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/dark_gray"
            android:text="@string/button_7"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_8"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/dark_gray"
            android:text="@string/button_8"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_9"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/dark_gray"
            android:text="@string/button_9"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_subtract"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/teal_700"
            android:text="@string/button_subtract"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <!-- Row 3: 4, 5, 6, + -->
        <Button
            android:id="@+id/btn_4"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/dark_gray"
            android:text="@string/button_4"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_5"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/dark_gray"
            android:text="@string/button_5"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_6"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/dark_gray"
            android:text="@string/button_6"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_add"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/teal_700"
            android:text="@string/button_add"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <!-- Row 4: 1, 2, 3, = (spans 2 rows) -->
        <Button
            android:id="@+id/btn_1"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/dark_gray"
            android:text="@string/button_1"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_2"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/dark_gray"
            android:text="@string/button_2"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_3"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/dark_gray"
            android:text="@string/button_3"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_equals"
            android:layout_width="0dp"
            android:layout_height="168dp"
            android:layout_rowSpan="2"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/purple_700"
            android:text="@string/button_equals"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <!-- Row 5: 0 (spans 2 columns), . -->
        <Button
            android:id="@+id/btn_0"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnSpan="2"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/dark_gray"
            android:text="@string/button_0"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_decimal"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_columnWeight="1"
            android:layout_margin="4dp"
            android:backgroundTint="@color/dark_gray"
            android:text="@string/button_decimal"
            android:textColor="@color/white"
            android:textSize="24sp" />

    </GridLayout>

</LinearLayout>
