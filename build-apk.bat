@echo off
chcp 65001 >nul
echo ================================
echo    简单计算器 APK 构建工具
echo ================================
echo.

echo 正在清理项目...
call gradlew.bat clean

echo.
echo 正在构建调试版 APK...
call gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ APK 构建成功！
    echo.
    echo 📁 APK 文件位置：
    echo    %CD%\app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo 📱 安装到连接的设备：
    echo    adb install app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo 🔍 打开APK所在文件夹？ (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        explorer app\build\outputs\apk\debug\
    )
) else (
    echo.
    echo ❌ 构建失败，请检查错误信息
)

echo.
pause
