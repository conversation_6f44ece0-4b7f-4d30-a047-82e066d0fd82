package com.example.simplecalculator;

import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import java.text.DecimalFormat;

public class MainActivity extends AppCompatActivity implements View.OnClickListener {

    private TextView display;
    private String currentNumber = "";
    private String operator = "";
    private String previousNumber = "";
    private boolean operatorPressed = false;
    private boolean equalsPressed = false;
    
    private DecimalFormat decimalFormat = new DecimalFormat("#.########");

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Initialize display
        display = findViewById(R.id.display);

        // Initialize all buttons and set click listeners
        setupButtons();
    }

    private void setupButtons() {
        // Number buttons
        findViewById(R.id.btn_0).setOnClickListener(this);
        findViewById(R.id.btn_1).setOnClickListener(this);
        findViewById(R.id.btn_2).setOnClickListener(this);
        findViewById(R.id.btn_3).setOnClickListener(this);
        findViewById(R.id.btn_4).setOnClickListener(this);
        findViewById(R.id.btn_5).setOnClickListener(this);
        findViewById(R.id.btn_6).setOnClickListener(this);
        findViewById(R.id.btn_7).setOnClickListener(this);
        findViewById(R.id.btn_8).setOnClickListener(this);
        findViewById(R.id.btn_9).setOnClickListener(this);

        // Operator buttons
        findViewById(R.id.btn_add).setOnClickListener(this);
        findViewById(R.id.btn_subtract).setOnClickListener(this);
        findViewById(R.id.btn_multiply).setOnClickListener(this);
        findViewById(R.id.btn_divide).setOnClickListener(this);
        findViewById(R.id.btn_equals).setOnClickListener(this);

        // Other buttons
        findViewById(R.id.btn_clear).setOnClickListener(this);
        findViewById(R.id.btn_decimal).setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        Button button = (Button) view;
        String buttonText = button.getText().toString();

        int id = view.getId();

        if (id == R.id.btn_clear) {
            clear();
        } else if (id == R.id.btn_equals) {
            calculateResult();
        } else if (id == R.id.btn_add || id == R.id.btn_subtract || 
                   id == R.id.btn_multiply || id == R.id.btn_divide) {
            handleOperator(buttonText);
        } else if (id == R.id.btn_decimal) {
            handleDecimal();
        } else {
            // Number buttons
            handleNumber(buttonText);
        }
    }

    private void handleNumber(String number) {
        if (equalsPressed) {
            clear();
            equalsPressed = false;
        }
        
        if (operatorPressed) {
            currentNumber = "";
            operatorPressed = false;
        }
        
        currentNumber += number;
        updateDisplay(currentNumber);
    }

    private void handleOperator(String op) {
        if (!currentNumber.isEmpty()) {
            if (!previousNumber.isEmpty() && !operatorPressed) {
                calculateResult();
            } else {
                previousNumber = currentNumber;
            }
        }
        
        operator = op;
        operatorPressed = true;
        equalsPressed = false;
    }

    private void handleDecimal() {
        if (equalsPressed) {
            clear();
            equalsPressed = false;
        }
        
        if (operatorPressed) {
            currentNumber = "";
            operatorPressed = false;
        }
        
        if (currentNumber.isEmpty()) {
            currentNumber = "0";
        }
        
        if (!currentNumber.contains(".")) {
            currentNumber += ".";
            updateDisplay(currentNumber);
        }
    }

    private void calculateResult() {
        if (previousNumber.isEmpty() || operator.isEmpty() || currentNumber.isEmpty()) {
            return;
        }

        try {
            double num1 = Double.parseDouble(previousNumber);
            double num2 = Double.parseDouble(currentNumber);
            double result = 0;

            switch (operator) {
                case "+":
                    result = num1 + num2;
                    break;
                case "-":
                    result = num1 - num2;
                    break;
                case "×":
                    result = num1 * num2;
                    break;
                case "÷":
                    if (num2 != 0) {
                        result = num1 / num2;
                    } else {
                        updateDisplay(getString(R.string.error_message));
                        return;
                    }
                    break;
            }

            String resultString = decimalFormat.format(result);
            updateDisplay(resultString);
            
            currentNumber = resultString;
            previousNumber = "";
            operator = "";
            equalsPressed = true;
            
        } catch (NumberFormatException e) {
            updateDisplay(getString(R.string.error_message));
            clear();
        }
    }

    private void clear() {
        currentNumber = "";
        previousNumber = "";
        operator = "";
        operatorPressed = false;
        equalsPressed = false;
        updateDisplay("0");
    }

    private void updateDisplay(String text) {
        display.setText(text);
    }
}
