<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="black">#FF000000</color>
    <color name="dark_gray">#FF424242</color>
    <color name="light_gray">#FFF5F5F5</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">简单计算器</string>
    <string name="button_0">0</string>
    <string name="button_1">1</string>
    <string name="button_2">2</string>
    <string name="button_3">3</string>
    <string name="button_4">4</string>
    <string name="button_5">5</string>
    <string name="button_6">6</string>
    <string name="button_7">7</string>
    <string name="button_8">8</string>
    <string name="button_9">9</string>
    <string name="button_add">+</string>
    <string name="button_clear">清除</string>
    <string name="button_decimal">.</string>
    <string name="button_divide">÷</string>
    <string name="button_equals">=</string>
    <string name="button_multiply">×</string>
    <string name="button_subtract">-</string>
    <string name="display_hint">0</string>
    <string name="error_message">错误</string>
    <style name="Theme.SimpleCalculator" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style>
</resources>