@echo off
chcp 65001 >nul
echo ================================
echo    APK 安装工具
echo ================================
echo.

set APK_PATH=app\build\outputs\apk\debug\app-debug.apk

if not exist "%APK_PATH%" (
    echo ❌ 找不到 APK 文件：%APK_PATH%
    echo 请先运行 build-apk.bat 构建 APK
    goto :end
)

echo 检查连接的设备...
adb devices

echo.
echo 正在安装 APK 到连接的设备...
adb install -r "%APK_PATH%"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 安装成功！
    echo.
    echo 🚀 启动应用？ (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo 正在启动应用...
        adb shell am start -n com.example.simplecalculator/.MainActivity
    )
) else (
    echo.
    echo ❌ 安装失败
    echo 请确保：
    echo 1. 手机已连接并启用USB调试
    echo 2. 手机允许安装未知来源应用
    echo 3. 运行 'adb devices' 确认设备已识别
)

:end
echo.
pause
