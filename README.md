# 简单计算器 Android 应用

一个使用 Java 17 构建的基础计算器 Android 应用程序，演示了 Android 开发的基本概念。兼容 Java 21 运行环境。

## 功能特性

- 基本算术运算（加法、减法、乘法、除法）
- 支持小数运算
- 清除功能
- 除零错误处理
- 现代化 Material Design 界面
- 响应式按钮布局

## 项目结构

```
android/
├── app/
│   ├── build.gradle                 # 应用级构建配置
│   ├── proguard-rules.pro          # ProGuard 配置
│   └── src/main/
│       ├── AndroidManifest.xml     # 应用清单文件
│       ├── java/com/example/simplecalculator/
│       │   └── MainActivity.java   # 包含计算器逻辑的主活动
│       └── res/
│           ├── layout/
│           │   └── activity_main.xml    # 主界面布局
│           ├── values/
│           │   ├── colors.xml           # 颜色定义
│           │   ├── strings.xml          # 字符串资源
│           │   └── themes.xml           # 应用主题
│           ├── mipmap-hdpi/            # 应用图标
│           └── xml/                     # 备份和数据提取规则
├── build.gradle                    # 项目级构建配置
├── gradle.properties              # Gradle 属性
└── settings.gradle                # 项目设置
```

## 前置要求

在构建和运行应用程序之前，请确保您拥有：

1. **Java 开发工具包 (JDK) 17 或更高版本** - 您已经安装了 Java 21.0.6 ✓（完全兼容）
2. **Android Studio**（推荐）或 Android SDK 命令行工具
3. **Android SDK** 包含以下组件：
   - Android SDK Platform 34 (Android 14)
   - Android SDK Build-Tools 34.0.0 或更高版本
   - Android SDK Platform-Tools
4. **Android 设备** 或 **Android 虚拟设备 (AVD)** 用于测试

## 安装说明

### 方式一：使用 Android Studio（推荐）

1. **安装 Android Studio**：
   - 从以下地址下载：https://developer.android.com/studio
   - 按照安装向导进行安装
   - 在提示时安装 Android SDK

2. **打开项目**：
   - 启动 Android Studio
   - 选择"打开现有项目"
   - 导航到您的 `d:\Desktop\android` 目录
   - 点击"确定"

3. **同步项目**：
   - Android Studio 会自动检测 Gradle 文件
   - 如果提示，点击"立即同步"
   - 等待同步完成

4. **配置 SDK**：
   - 转到 文件 → 项目结构 → SDK 位置
   - 确保 Android SDK 位置设置正确
   - 如果提示，安装任何缺失的 SDK 组件

### 方式二：使用命令行

1. **安装 Android SDK**：
   - 下载 Android SDK 命令行工具
   - 设置 ANDROID_HOME 环境变量
   - 将 SDK 工具添加到您的 PATH

2. **安装所需的 SDK 组件**：
   ```bash
   sdkmanager "platforms;android-34"
   sdkmanager "build-tools;34.0.0"
   sdkmanager "platform-tools"
   ```

## 构建应用程序

### 使用 Android Studio

1. **构建项目**：
   - 点击 构建 → 生成项目 (Ctrl+F9)
   - 或点击 构建 → 构建 Bundle(s) / APK(s) → 构建 APK(s)

2. **检查错误**：
   - 查看构建选项卡中的任何编译错误
   - 修复出现的任何问题

### 使用命令行

1. **导航到项目目录**：
   ```bash
   cd d:\Desktop\android
   ```

2. **构建调试 APK**：
   ```bash
   ./gradlew assembleDebug
   ```

   在 Windows 上：
   ```cmd
   gradlew.bat assembleDebug
   ```

3. **构建发布 APK**：
   ```bash
   ./gradlew assembleRelease
   ```

## 运行应用程序

### 使用 Android Studio

1. **设置设备/模拟器**：
   - **物理设备**：在开发者选项中启用 USB 调试
   - **模拟器**：使用 AVD 管理器创建 AVD（工具 → AVD 管理器）

2. **运行应用**：
   - 点击运行按钮（绿色三角形）或按 Shift+F10
   - 选择您的目标设备
   - 应用将自动安装并启动

### 使用命令行

1. **在连接的设备上安装**：
   ```bash
   adb install app/build/outputs/apk/debug/app-debug.apk
   ```

2. **启动应用**：
   ```bash
   adb shell am start -n com.example.simplecalculator/.MainActivity
   ```

## 📱 打包APK安装包

### 快速打包（推荐）

项目包含便捷的批处理脚本：

1. **构建APK**：
   ```cmd
   双击运行 build-apk.bat
   ```
   - 自动清理项目并构建调试版APK
   - 构建完成后会显示APK文件位置

2. **安装到手机**：
   ```cmd
   双击运行 install-apk.bat
   ```
   - 自动安装APK到连接的Android设备
   - 可选择安装后直接启动应用

### 手动打包方式

#### 方式一：Android Studio界面操作

1. **构建调试版APK**：
   - 菜单栏：**Build** → **Build Bundle(s) / APK(s)** → **Build APK(s)**
   - APK生成位置：`app\build\outputs\apk\debug\app-debug.apk`

2. **构建发布版APK**：
   - 菜单栏：**Build** → **Generate Signed Bundle / APK**
   - 选择 **APK** → **Next**
   - 创建或选择签名密钥
   - 选择 **release** 构建类型
   - APK生成位置：`app\build\outputs\apk\release\app-release.apk`

#### 方式二：命令行操作

```cmd
# 构建调试版
gradlew.bat assembleDebug

# 构建发布版（需要先配置签名）
gradlew.bat assembleRelease
```

### 在手机上安装APK

#### 方法一：USB连接安装

1. **启用开发者选项**：
   - 设置 → 关于手机 → 连续点击"版本号"7次

2. **启用USB调试**：
   - 设置 → 开发者选项 → 开启"USB调试"

3. **连接并安装**：
   ```cmd
   adb install app\build\outputs\apk\debug\app-debug.apk
   ```

#### 方法二：直接安装

1. **传输APK到手机**：
   - 通过USB复制APK文件到手机
   - 或通过QQ、微信等发送给自己

2. **允许未知来源**：
   - 设置 → 安全 → 允许安装未知来源应用

3. **点击APK文件安装**

## 测试应用程序

### 手动测试

1. **基本运算**：
   - 测试加法：5 + 3 = 8
   - 测试减法：10 - 4 = 6
   - 测试乘法：7 × 8 = 56
   - 测试除法：15 ÷ 3 = 5

2. **边界情况**：
   - 除零运算（应显示"错误"）
   - 小数运算：3.5 + 2.1 = 5.6
   - 清除功能重置显示

3. **界面测试**：
   - 所有按钮响应触摸
   - 显示正确更新
   - 布局在不同屏幕尺寸上显示良好

## Java 版本兼容性说明

此项目已更新以支持现代 Java 版本：

- **目标 Java 版本**：Java 17（Android 推荐）
- **兼容 Java 版本**：Java 17 - Java 21
- **Gradle 版本**：8.5（支持 Java 21）
- **Android Gradle Plugin**：8.2.0

如果您使用的是 Java 21（如您的情况），项目应该可以正常工作。如果遇到兼容性问题，请参考下面的故障排除部分。

## 故障排除

### 常见问题

1. **Gradle 同步失败**：
   - 检查网络连接
   - 验证 Gradle 版本兼容性
   - 清理并重新构建：构建 → 清理项目

2. **找不到 SDK**：
   - 安装所需的 SDK 组件
   - 检查 ANDROID_HOME 环境变量
   - 在 Android Studio 中验证 SDK 路径

3. **Java 版本兼容性问题**：
   - 项目现在配置为使用 Java 17，兼容 Java 21
   - 如果遇到 "incompatible Java version" 错误：
     * 确保使用 Gradle 8.5 或更高版本
     * 检查 文件 → 项目结构 → SDK 位置 → JDK 位置
     * 在 Android Studio 中：文件 → 设置 → 构建、执行、部署 → 构建工具 → Gradle → Gradle JVM 选择正确的 JDK

4. **构建错误**：
   - 检查 Java 代码中的语法错误
   - 验证所有资源文件格式正确
   - 清理并重新构建项目

### 获取帮助

- 查看 Android Studio 的构建选项卡获取详细错误信息
- 查看 Gradle 控制台了解构建问题
- 查阅 Android 开发者文档：https://developer.android.com/docs

## 下一步

一旦您的基础计算器正常工作，可以考虑以下增强功能：

1. 添加更多高级运算（平方根、百分比等）
2. 实现计算历史记录
3. 添加键盘输入支持
4. 为计算器逻辑创建单元测试
5. 通过动画和更好的样式改进界面
6. 添加横屏方向支持

## 许可证

此项目为教育目的而创建，可自由使用和修改。
