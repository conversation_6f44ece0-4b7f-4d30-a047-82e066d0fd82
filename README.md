# Simple Calculator Android App

A basic calculator Android application built with Java 11, demonstrating fundamental Android development concepts.

## Features

- Basic arithmetic operations (addition, subtraction, multiplication, division)
- Decimal number support
- Clear function
- Error handling for division by zero
- Modern Material Design UI
- Responsive button layout

## Project Structure

```
android/
├── app/
│   ├── build.gradle                 # App-level build configuration
│   ├── proguard-rules.pro          # ProGuard configuration
│   └── src/main/
│       ├── AndroidManifest.xml     # App manifest
│       ├── java/com/example/simplecalculator/
│       │   └── MainActivity.java   # Main activity with calculator logic
│       └── res/
│           ├── layout/
│           │   └── activity_main.xml    # Main UI layout
│           ├── values/
│           │   ├── colors.xml           # Color definitions
│           │   ├── strings.xml          # String resources
│           │   └── themes.xml           # App themes
│           ├── mipmap-hdpi/            # App icons
│           └── xml/                     # Backup and data extraction rules
├── build.gradle                    # Project-level build configuration
├── gradle.properties              # Gradle properties
└── settings.gradle                # Project settings
```

## Prerequisites

Before building and running the application, ensure you have:

1. **Java Development Kit (JDK) 11** - You already have OpenJDK 11.0.1 installed ✓
2. **Android Studio** (recommended) or Android SDK command-line tools
3. **Android SDK** with the following components:
   - Android SDK Platform 34 (Android 14)
   - Android SDK Build-Tools 34.0.0 or higher
   - Android SDK Platform-Tools
4. **Android device** or **Android Virtual Device (AVD)** for testing

## Setup Instructions

### Option 1: Using Android Studio (Recommended)

1. **Install Android Studio**:
   - Download from: https://developer.android.com/studio
   - Follow the installation wizard
   - Install the Android SDK when prompted

2. **Open the Project**:
   - Launch Android Studio
   - Select "Open an existing project"
   - Navigate to your `d:\Desktop\android` directory
   - Click "OK"

3. **Sync Project**:
   - Android Studio will automatically detect the Gradle files
   - Click "Sync Now" if prompted
   - Wait for the sync to complete

4. **Configure SDK**:
   - Go to File → Project Structure → SDK Location
   - Ensure Android SDK location is set correctly
   - Install any missing SDK components if prompted

### Option 2: Using Command Line

1. **Install Android SDK**:
   - Download Android SDK command-line tools
   - Set ANDROID_HOME environment variable
   - Add SDK tools to your PATH

2. **Install Required SDK Components**:
   ```bash
   sdkmanager "platforms;android-34"
   sdkmanager "build-tools;34.0.0"
   sdkmanager "platform-tools"
   ```

## Building the Application

### Using Android Studio

1. **Build the Project**:
   - Click Build → Make Project (Ctrl+F9)
   - Or click Build → Build Bundle(s) / APK(s) → Build APK(s)

2. **Check for Errors**:
   - Review the Build tab for any compilation errors
   - Fix any issues that arise

### Using Command Line

1. **Navigate to Project Directory**:
   ```bash
   cd d:\Desktop\android
   ```

2. **Build Debug APK**:
   ```bash
   ./gradlew assembleDebug
   ```
   
   On Windows:
   ```cmd
   gradlew.bat assembleDebug
   ```

3. **Build Release APK**:
   ```bash
   ./gradlew assembleRelease
   ```

## Running the Application

### Using Android Studio

1. **Set up Device/Emulator**:
   - **Physical Device**: Enable USB debugging in Developer Options
   - **Emulator**: Create an AVD using AVD Manager (Tools → AVD Manager)

2. **Run the App**:
   - Click the Run button (green triangle) or press Shift+F10
   - Select your target device
   - The app will install and launch automatically

### Using Command Line

1. **Install on Connected Device**:
   ```bash
   adb install app/build/outputs/apk/debug/app-debug.apk
   ```

2. **Launch the App**:
   ```bash
   adb shell am start -n com.example.simplecalculator/.MainActivity
   ```

## Testing the Application

### Manual Testing

1. **Basic Operations**:
   - Test addition: 5 + 3 = 8
   - Test subtraction: 10 - 4 = 6
   - Test multiplication: 7 × 8 = 56
   - Test division: 15 ÷ 3 = 5

2. **Edge Cases**:
   - Division by zero (should show "Error")
   - Decimal operations: 3.5 + 2.1 = 5.6
   - Clear function resets display

3. **UI Testing**:
   - All buttons respond to touch
   - Display updates correctly
   - Layout looks good on different screen sizes

## Troubleshooting

### Common Issues

1. **Gradle Sync Failed**:
   - Check internet connection
   - Verify Gradle version compatibility
   - Clean and rebuild: Build → Clean Project

2. **SDK Not Found**:
   - Install required SDK components
   - Check ANDROID_HOME environment variable
   - Verify SDK path in Android Studio

3. **Java Version Issues**:
   - Ensure Java 11 is being used
   - Check File → Project Structure → SDK Location → JDK location

4. **Build Errors**:
   - Check for syntax errors in Java code
   - Verify all resource files are properly formatted
   - Clean and rebuild the project

### Getting Help

- Check Android Studio's Build tab for detailed error messages
- Review the Gradle Console for build issues
- Consult Android Developer documentation: https://developer.android.com/docs

## Next Steps

Once you have the basic calculator working, consider these enhancements:

1. Add more advanced operations (square root, percentage, etc.)
2. Implement calculation history
3. Add keyboard input support
4. Create unit tests for the calculator logic
5. Improve the UI with animations and better styling
6. Add landscape orientation support

## License

This project is created for educational purposes and is free to use and modify.
