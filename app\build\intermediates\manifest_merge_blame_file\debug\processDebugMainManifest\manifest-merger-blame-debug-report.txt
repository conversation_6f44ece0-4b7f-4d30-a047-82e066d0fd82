1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.simplecalculator"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c78b3d8c1a940b2bc777d584e2f71ec9\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.simplecalculator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c78b3d8c1a940b2bc777d584e2f71ec9\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c78b3d8c1a940b2bc777d584e2f71ec9\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.simplecalculator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c78b3d8c1a940b2bc777d584e2f71ec9\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c78b3d8c1a940b2bc777d584e2f71ec9\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\Desktop\android\app\src\main\AndroidManifest.xml:5:5-25:19
18        android:allowBackup="true"
18-->D:\Desktop\android\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c78b3d8c1a940b2bc777d584e2f71ec9\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\Desktop\android\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->D:\Desktop\android\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->D:\Desktop\android\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->D:\Desktop\android\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->D:\Desktop\android\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->D:\Desktop\android\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.SimpleCalculator" >
29-->D:\Desktop\android\app\src\main\AndroidManifest.xml:13:9-54
30        <activity
30-->D:\Desktop\android\app\src\main\AndroidManifest.xml:15:9-24:20
31            android:name="com.example.simplecalculator.MainActivity"
31-->D:\Desktop\android\app\src\main\AndroidManifest.xml:16:13-41
32            android:exported="true"
32-->D:\Desktop\android\app\src\main\AndroidManifest.xml:17:13-36
33            android:theme="@style/Theme.SimpleCalculator" >
33-->D:\Desktop\android\app\src\main\AndroidManifest.xml:18:13-58
34            <intent-filter>
34-->D:\Desktop\android\app\src\main\AndroidManifest.xml:19:13-23:29
35                <action android:name="android.intent.action.MAIN" />
35-->D:\Desktop\android\app\src\main\AndroidManifest.xml:20:17-69
35-->D:\Desktop\android\app\src\main\AndroidManifest.xml:20:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->D:\Desktop\android\app\src\main\AndroidManifest.xml:22:17-77
37-->D:\Desktop\android\app\src\main\AndroidManifest.xml:22:27-74
38            </intent-filter>
39        </activity>
40
41        <provider
41-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56a233e3b3e09590de7f8d8978f4284f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
42            android:name="androidx.startup.InitializationProvider"
42-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56a233e3b3e09590de7f8d8978f4284f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
43            android:authorities="com.example.simplecalculator.androidx-startup"
43-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56a233e3b3e09590de7f8d8978f4284f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
44            android:exported="false" >
44-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56a233e3b3e09590de7f8d8978f4284f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
45            <meta-data
45-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56a233e3b3e09590de7f8d8978f4284f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
46                android:name="androidx.emoji2.text.EmojiCompatInitializer"
46-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56a233e3b3e09590de7f8d8978f4284f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
47                android:value="androidx.startup" />
47-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56a233e3b3e09590de7f8d8978f4284f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
48            <meta-data
48-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\746ba293ce9e2531e8f7acd2f3c7c434\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
49                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
49-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\746ba293ce9e2531e8f7acd2f3c7c434\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
50                android:value="androidx.startup" />
50-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\746ba293ce9e2531e8f7acd2f3c7c434\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
51            <meta-data
51-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
52                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
52-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
53                android:value="androidx.startup" />
53-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
54        </provider>
55
56        <receiver
56-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
57            android:name="androidx.profileinstaller.ProfileInstallReceiver"
57-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
58            android:directBootAware="false"
58-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
59            android:enabled="true"
59-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
60            android:exported="true"
60-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
61            android:permission="android.permission.DUMP" >
61-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
62            <intent-filter>
62-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
63                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
64            </intent-filter>
65            <intent-filter>
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
66                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
67            </intent-filter>
68            <intent-filter>
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
69                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
70            </intent-filter>
71            <intent-filter>
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
72                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cc6188dc13890cc75612d486ebd68ff\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
73            </intent-filter>
74        </receiver>
75    </application>
76
77</manifest>
